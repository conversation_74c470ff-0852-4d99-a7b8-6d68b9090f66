<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border show-summary :summary-method="summaryMethod">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="交易编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.bizId`" :rules="formRules.bizId" class="mb-0px!">
            <el-input v-model="row.bizId" placeholder="请输入交易编号" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="交易单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.bizNo`" :rules="formRules.bizNo" class="mb-0px!">
            <el-input v-model="row.bizNo" placeholder="请输入交易单号" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="交易明细ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.bizDetailId`" :rules="formRules.bizDetailId" class="mb-0px!">
            <el-input v-model="row.bizDetailId" placeholder="请输入交易明细ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="交易类型" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.transactionType`" :rules="formRules.transactionType" class="mb-0px!">
            <el-select v-model="row.transactionType" placeholder="请选择交易类型">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="交易方向" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.transactionDirection`" :rules="formRules.transactionDirection" class="mb-0px!">
            <el-select v-model="row.transactionDirection" placeholder="请选择交易方向">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <el-input v-model="row.materialId" placeholder="请输入物料ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="物料编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <ScrollSelect
              filterable
              clearable
              class="!w-180px"
              v-model="row.materialId"
              placeholder="请选择物料"
              :initial-load="false"
              :load-method="getRemoteMaterial"
              :label-key="formatMaterialLabel"
              value-key="id"
              query-key="name"
              :default-value="{id: row.materialId, name: row.materialName, fullCode: row.materialCode, spec: row.materialSpec}"
              @change="(val, material) => onMaterialChange(row, val, material)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料类型" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialType`" :rules="formRules.materialType" class="mb-0px!">
            <el-select v-model="row.materialType" placeholder="请选择物料类型" disabled>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料规格" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialSpec`" :rules="formRules.materialSpec" class="mb-0px!">
            <el-input v-model="row.materialSpec" placeholder="请输入物料规格" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="移动类型" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.moveType`" :rules="formRules.moveType" class="mb-0px!">
            <el-select v-model="row.moveType" placeholder="请选择移动类型">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.INVENTORY_MOVE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动源仓库ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fromWarehouseId`" :rules="formRules.fromWarehouseId" class="mb-0px!">
            <el-input v-model="row.fromWarehouseId" placeholder="请输入移动源仓库ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="移动源仓库" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fromWarehouseName`" :rules="formRules.fromWarehouseName" class="mb-0px!">
            <el-tree-select
              v-model="row.fromWarehouseName"
              :data="warehouseTreeData"
              placeholder="请选择源仓库"
              clearable
              check-strictly
              :render-after-expand="false"
              class="!w-160px"
              node-key="id"
              :props="{
                value: 'name',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="(val) => onFromWarehouseChange(row, val)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动源仓位ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fromLocationId`" :rules="formRules.fromLocationId" class="mb-0px!">
            <el-input v-model="row.fromLocationId" placeholder="请输入移动源仓位ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="移动源仓位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fromLocationName`" :rules="formRules.fromLocationName" class="mb-0px!">
            <el-select
              v-model="row.fromLocationName"
              placeholder="请选择源仓位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="location in getFilteredLocations(row.fromWarehouseId)"
                :key="location.id"
                :label="location.name"
                :value="location.name"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动到仓库ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.toWarehouseId`" :rules="formRules.toWarehouseId" class="mb-0px!">
            <el-input v-model="row.toWarehouseId" placeholder="请输入移动到仓库ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="移动到仓库" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.toWarehouseName`" :rules="formRules.toWarehouseName" class="mb-0px!">
            <el-tree-select
              v-model="row.toWarehouseName"
              :data="warehouseTreeData"
              placeholder="请选择目标仓库"
              clearable
              check-strictly
              :render-after-expand="false"
              class="!w-160px"
              node-key="id"
              :props="{
                value: 'name',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="(val: string) => onToWarehouseChange(row, val)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动到仓位ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.toLocationId`" :rules="formRules.toLocationId" class="mb-0px!">
            <el-input v-model="row.toLocationId" placeholder="请输入移动到仓位ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="移动到仓位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.toLocationName`" :rules="formRules.toLocationName" class="mb-0px!">
            <el-select
              v-model="row.toLocationName"
              placeholder="请选择目标仓位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="location in getFilteredLocations(row.toWarehouseId)"
                :key="location.id"
                :label="location.name"
                :value="location.name"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="移动日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.moveDate`" :rules="formRules.moveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.moveDate"
              type="date"
              value-format="x"
              placeholder="选择移动日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="来源单号ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceId`" :rules="formRules.sourceId" class="mb-0px!">
            <el-input v-model="row.sourceId" placeholder="请输入来源单号ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="来源单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceNo`" :rules="formRules.sourceNo" class="mb-0px!">
            <el-input v-model="row.sourceNo" placeholder="请输入来源单号" />
          </el-form-item>
        </template>
      </el-table-column>
       <el-table-column label="库存批号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.inventoryBatchNo`" :rules="formRules.inventoryBatchNo" class="mb-0px!">
            <el-input v-model="row.inventoryBatchNo" placeholder="请输入库存批号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量" min-width="150" prop="quantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input v-model="row.quantity" placeholder="请输入数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantityUnit`" :rules="formRules.quantityUnit" class="mb-0px!">
            <el-select
              v-model="row.quantityUnit"
              placeholder="请选择单位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位数量" min-width="150" prop="auxiliaryQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.auxiliaryQuantity`" :rules="formRules.auxiliaryQuantity" class="mb-0px!">
            <el-input v-model="row.auxiliaryQuantity" placeholder="请输入基本单位数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.auxiliaryUnit`" :rules="formRules.auxiliaryUnit" class="mb-0px!">
            <el-select
              v-model="row.auxiliaryUnit"
              placeholder="请选择基本单位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="出入库前库存数量" min-width="150" prop="beforeQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.beforeQuantity`" :rules="formRules.beforeQuantity" class="mb-0px!">
            <el-input v-model="row.beforeQuantity" placeholder="请输入出入库前库存数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="出入库后库存数量" min-width="150" prop="afterQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.afterQuantity`" :rules="formRules.afterQuantity" class="mb-0px!">
            <el-input v-model="row.afterQuantity" placeholder="请输入出入库后库存数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectCode`" :rules="formRules.costObjectCode" class="mb-0px!">
            <el-input v-model="row.costObjectCode" placeholder="请输入成本对象编码" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">
            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="记账凭证号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.accountingVoucherNumber`" :rules="formRules.accountingVoucherNumber" class="mb-0px!">
            <el-input v-model="row.accountingVoucherNumber" placeholder="请输入记账凭证号" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="租户名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.tenantName`" :rules="formRules.tenantName" class="mb-0px!">
            <el-input v-model="row.tenantName" placeholder="请输入租户名称" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <Icon icon="ep:delete"  @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加库存交易明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { StockInfoApi } from '@/api/scm/inventory/stockinfo'
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
import { getRemoteMaterial, getRemoteUnit } from '@/utils/commonBiz'
import { WarehouseApi, WarehouseVO } from '@/api/scm/inventory/warehouse'
import { WarehouseLocationApi, WarehouseLocationVO } from '@/api/scm/inventory/warehouselocation'
import { handleTree } from '@/utils/tree'
import { formatQuantity } from '@/utils/formatter'

const props = defineProps<{
  inventoryId?: number // 库存ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])

// 仓库和仓位数据
const warehouseList = ref<WarehouseVO[]>([]) // 仓库列表数据
const warehouseTreeData = ref<any[]>([]) // 仓库树形数据
const locationList = ref<WarehouseLocationVO[]>([]) // 仓位列表数据

// 单位数据
const unitList = ref<any[]>([]) // 单位列表数据

/** 格式化物料选项显示标签 */
const formatMaterialLabel = (material: any) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (fullCode) parts.push(fullCode)
  if (name) parts.push(name)
  if (spec) parts.push(spec)

  return parts.join(' - ')
}

const formRules = reactive({
  bizId: [],
  bizNo: [],
  transactionType: [],
  transactionDirection: [],
  materialId: [],
  materialCode: [],
  materialName: [],
  materialType: [],
  moveType: [],
  fromWarehouseName: [],
  fromLocationName: [],
  toWarehouseName: [],
  toLocationName: [],
  moveDate: [],
  sourceNo: [],
  inventoryBatchNo: [],
  quantity: [],
  quantityUnit: [],
  auxiliaryQuantity: [],
  auxiliaryUnit: [],
  remark: [],
  beforeQuantity: [{ required: true, message: '出入库前库存数量不能为空', trigger: 'blur' }],
  afterQuantity: [{ required: true, message: '出入库后库存数量不能为空', trigger: 'blur' }],
  costObjectCode: [],
  costObjectName: [],
  accountingVoucherNumber: [],
  tenantName: [],
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.inventoryId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const data = await StockInfoApi.getTransactionListByInventoryId(val)
      
      // 确保单位ID是数字类型，方便选择器匹配
      formData.value = data.map((item: any) => ({
        ...item,
        quantityUnit: item.quantityUnit && typeof item.quantityUnit === 'string' 
          ? parseInt(item.quantityUnit) : item.quantityUnit,
        auxiliaryUnit: item.auxiliaryUnit && typeof item.auxiliaryUnit === 'string' 
          ? parseInt(item.auxiliaryUnit) : item.auxiliaryUnit
      }))
      
      console.log('TransactionForm 交易数据加载完成，数量:', formData.value.length)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row: any = {
    id: undefined,
    bizId: undefined,
    bizNo: undefined,
    bizDetailId: undefined,
    transactionType: undefined,
    transactionDirection: undefined,
    materialId: undefined,
    materialCode: undefined,
    materialName: undefined,
    materialType: undefined,
    moveType: undefined,
    fromWarehouseId: undefined,
    fromWarehouseName: undefined,
    fromLocationId: undefined,
    fromLocationName: undefined,
    toWarehouseId: undefined,
    toWarehouseName: undefined,
    toLocationId: undefined,
    toLocationName: undefined,
    moveDate: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    inventoryId: undefined,
    inventoryBatchNo: undefined,
    quantity: undefined,
    quantityUnit: undefined,
    auxiliaryQuantity: undefined,
    auxiliaryUnit: undefined,
    remark: undefined,
    beforeQuantity: undefined,
    afterQuantity: undefined,
    costObjectCode: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
    tenantName: undefined,
  }
  row.inventoryId = props.inventoryId
  formData.value.push(row)
}

/** 物料选择变化处理 */
const onMaterialChange = (row: any, val: any, material: any) => {
  if (material) {
    // 选择了物料，填充物料信息
    row.materialId = material.id
    row.materialName = material.name
    row.materialCode = material.fullCode
    row.materialSpec = material.spec
    row.materialType = material.type
  } else {
    // 清空了物料选择，清空相关字段
    row.materialId = undefined
    row.materialName = undefined
    row.materialCode = undefined
    row.materialSpec = undefined
    row.materialType = undefined
  }
}

/** 源仓库选择变化处理 */
const onFromWarehouseChange = (row: any, warehouseName: string) => {
  // 清空源仓位选择
  row.fromLocationName = undefined

  // 根据仓库名称找到仓库ID
  const selectedWarehouse = warehouseList.value.find(warehouse => warehouse.name === warehouseName)
  if (selectedWarehouse) {
    row.fromWarehouseId = selectedWarehouse.id
  } else {
    row.fromWarehouseId = undefined
  }
}

/** 目标仓库选择变化处理 */
const onToWarehouseChange = (row: any, warehouseName: string) => {
  // 清空目标仓位选择
  row.toLocationName = undefined

  // 根据仓库名称找到仓库ID
  const selectedWarehouse = warehouseList.value.find(warehouse => warehouse.name === warehouseName)
  if (selectedWarehouse) {
    row.toWarehouseId = selectedWarehouse.id
  } else {
    row.toWarehouseId = undefined
  }
}

/** 获取仓库列表 */
const loadWarehouses = async () => {
  try {
    const data = await WarehouseApi.getWarehouseList({
      pageNo: 1,
      pageSize: 100
    })
    warehouseList.value = data || []

    // 构建树形数据
    const treeData = handleTree(data || [], 'id', 'parentId')

    // 为树形数据添加禁用标识，只有叶子节点可以选择
    const markLeafNodes = (nodes: any[]): any[] => {
      return nodes.map(node => {
        const hasChildren = node.children && node.children.length > 0
        return {
          ...node,
          disabled: hasChildren, // 有子节点的节点禁用
          children: hasChildren ? markLeafNodes(node.children) : undefined
        }
      })
    }

    warehouseTreeData.value = markLeafNodes(treeData)
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    warehouseList.value = []
    warehouseTreeData.value = []
  }
}

/** 获取仓位列表 */
const loadLocations = async () => {
  try {
    const data = await WarehouseLocationApi.getWarehouseLocationPage({
      pageNo: 1,
      pageSize: 100
    })
    locationList.value = data.list || []
  } catch (error) {
    console.error('获取仓位列表失败:', error)
    locationList.value = []
  }
}

/** 获取单位列表 */
const loadUnits = async () => {
  try {
    unitList.value = await getRemoteUnit()
    console.log('TransactionForm 单位数据加载完成:', unitList.value.length, '个单位')
  } catch (error) {
    console.error('TransactionForm 获取单位数据失败:', error)
    unitList.value = []
  }
}

/** 根据仓库过滤仓位 */
const getFilteredLocations = (warehouseId: number) => {
  if (warehouseId) {
    return locationList.value.filter(location => location.warehouseId === warehouseId)
  }
  return locationList.value
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['quantity', 'auxiliaryQuantity', 'beforeQuantity', 'afterQuantity']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

// 初始化数据加载
onMounted(async () => {
  await Promise.all([
    loadWarehouses(),
    loadLocations(),
    loadUnits()
  ])
})

defineExpose({ validate, getData })
</script>
