<template>
	<view class="transaction-edit-container">
		<!-- 标题栏 -->
		<view class="header">
			<view class="title">编辑库存交易明细</view>
		</view>

		<!-- 表单内容 -->
		<scroll-view scroll-y class="form-content" v-if="!loading">
			<!-- 交易明细列表 -->
			<view class="transaction-list">
				<view v-for="(transaction, index) in transactionList" :key="index" class="transaction-item">
					<view class="item-header">
						<text class="item-index">明细 {{ index + 1 }}</text>
						<button class="delete-btn" @click="handleDelete(index)">删除</button>
					</view>

					<!-- 交易基本信息 -->
					<view class="form-section">
						<view class="section-title">交易信息</view>
						
						<uv-form-item label="交易编号">
							<uv-input
								v-model="transaction.bizId"
								placeholder="请输入交易编号"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="交易单号">
							<uv-input
								v-model="transaction.bizNo"
								placeholder="请输入交易单号"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="交易类型">
							<SelectPicker
								v-model="transaction.transactionType"
								:options="transactionTypeOptions || []"
								title="选择交易类型"
								label-field="label"
								value-field="value"
								placeholder="请选择交易类型"
							/>
						</uv-form-item>

						<uv-form-item label="交易方向">
							<SelectPicker
								v-model="transaction.transactionDirection"
								:options="transactionDirectionOptions || []"
								title="选择交易方向"
								label-field="label"
								value-field="value"
								placeholder="请选择交易方向"
							/>
						</uv-form-item>

						<uv-form-item label="移动类型">
							<SelectPicker
								v-model="transaction.moveType"
								:options="moveTypeOptions || []"
								title="选择移动类型"
								label-field="label"
								value-field="value"
								placeholder="请选择移动类型"
							/>
						</uv-form-item>
					</view>

					<!-- 物料信息 -->
					<view class="form-section">
						<view class="section-title">物料信息</view>
						
						<uv-form-item label="物料编码">
							<uv-input
								v-model="transaction.materialCode"
								placeholder="物料编码"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="物料规格">
							<uv-input
								v-model="transaction.materialSpec"
								placeholder="物料规格"
								disabled
							/>
						</uv-form-item>
					</view>

					<!-- 仓库信息 -->
					<view class="form-section">
						<view class="section-title">仓库信息</view>
						
						<uv-form-item label="来源仓库">
							<uv-input
								v-model="transaction.fromWarehouseName"
								placeholder="来源仓库"
							/>
						</uv-form-item>

						<uv-form-item label="来源仓位">
							<uv-input
								v-model="transaction.fromLocationName"
								placeholder="来源仓位"
							/>
						</uv-form-item>

						<uv-form-item label="目标仓库">
							<uv-input
								v-model="transaction.toWarehouseName"
								placeholder="目标仓库"
							/>
						</uv-form-item>

						<uv-form-item label="目标仓位">
							<uv-input
								v-model="transaction.toLocationName"
								placeholder="目标仓位"
							/>
						</uv-form-item>
					</view>

					<!-- 数量信息 -->
					<view class="form-section">
						<view class="section-title">数量信息</view>
						
						<uv-form-item label="数量">
							<uv-input
								v-model="transaction.quantity"
								type="number"
								placeholder="请输入数量"
							/>
						</uv-form-item>

						<uv-form-item label="数量单位">
							<SelectPicker
								v-model="transaction.quantityUnit"
								:options="unitOptions || []"
								title="选择数量单位"
								label-field="name"
								value-field="id"
								placeholder="请选择数量单位"
							/>
						</uv-form-item>

						<uv-form-item label="基本单位数量">
							<uv-input
								v-model="transaction.auxiliaryQuantity"
								type="number"
								placeholder="请输入基本单位数量"
							/>
						</uv-form-item>

						<uv-form-item label="基本单位">
							<SelectPicker
								v-model="transaction.auxiliaryUnit"
								:options="unitOptions || []"
								title="选择基本单位"
								label-field="name"
								value-field="id"
								placeholder="请选择基本单位"
							/>
						</uv-form-item>

						<uv-form-item label="出入库前数量">
							<uv-input
								v-model="transaction.beforeQuantity"
								type="number"
								placeholder="请输入出入库前数量"
							/>
						</uv-form-item>

						<uv-form-item label="出入库后数量">
							<uv-input
								v-model="transaction.afterQuantity"
								type="number"
								placeholder="请输入出入库后数量"
							/>
						</uv-form-item>
					</view>

					<!-- 其他信息 -->
					<view class="form-section">
						<view class="section-title">其他信息</view>
						
						<uv-form-item label="移动日期">
							<uni-datetime-picker
								v-model="transaction.moveDate"
								type="date"
								placeholder="请选择移动日期"
								:clear-icon="false"
							/>
						</uv-form-item>

						<uv-form-item label="来源单号">
							<uv-input
								v-model="transaction.sourceNo"
								placeholder="请输入来源单号"
							/>
						</uv-form-item>

						<uv-form-item label="库存批号">
							<uv-input
								v-model="transaction.inventoryBatchNo"
								placeholder="请输入库存批号"
							/>
						</uv-form-item>

						<uv-form-item label="成本对象编码">
							<uv-input
								v-model="transaction.costObjectCode"
								placeholder="请输入成本对象编码"
							/>
						</uv-form-item>

						<uv-form-item label="成本对象名称">
							<uv-input
								v-model="transaction.costObjectName"
								placeholder="请输入成本对象名称"
							/>
						</uv-form-item>

						<uv-form-item label="记账凭证号">
							<uv-input
								v-model="transaction.accountingVoucherNumber"
								placeholder="请输入记账凭证号"
							/>
						</uv-form-item>

						<uv-form-item label="备注">
							<uv-input
								v-model="transaction.remark"
								type="textarea"
								placeholder="请输入备注"
								maxlength="200"
								:autoHeight="true"
							/>
						</uv-form-item>
					</view>
				</view>
			</view>

			<!-- 添加按钮 -->
			<view class="add-section">
				<button class="add-btn" @click="handleAdd">+ 添加交易明细</button>
			</view>
		</scroll-view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="cancel-button" @click="handleCancel">取消</button>
			<button class="save-button" :disabled="saving" @click="handleSave">
				<text v-if="saving">保存中...</text>
				<text v-else>保存</text>
			</button>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { getBatchDictOptions, DICT_TYPE } from '@/utils/dict.js'
import { stockInfoApi } from '@/api/scm/inventory/stockInfo/index.js'
import SelectPicker from '@/components/SelectPicker/SelectPicker.vue'

export default {
	components: {
		SelectPicker
	},
	data() {
		return {
			stockId: null,
			loading: false,
			saving: false,
			transactionList: [],
			// 字典数据
			transactionTypeOptions: [],
			transactionDirectionOptions: [],
			moveTypeOptions: [],
			unitOptions: [],
			dictData: {}
		}
	},
	async onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if(eventChannel){
			eventChannel.on('acceptDataFormOpener',(data) => {
				if(data) {
					this.stockId = data.stockId
					this.transactionList = data.transactionList || []
					// 初始化数据
					this.initData()
				}
			})
		}
	},
	methods: {
		// 初始化数据
		async initData() {
			await this.getDictData()
		},

		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION,
					DICT_TYPE.INVENTORY_MOVE_TYPE
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.transactionTypeOptions = this.dictData[DICT_TYPE.SCM_BIZ_TYPE] || []
				this.transactionDirectionOptions = this.dictData[DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION] || []
				this.moveTypeOptions = this.dictData[DICT_TYPE.INVENTORY_MOVE_TYPE] || []

				// 加载单位数据
				await this.loadUnits()

			} catch (error) {
				// 字典数据加载失败不影响主要功能
				this.dictData = {}
				this.transactionTypeOptions = []
				this.transactionDirectionOptions = []
				this.moveTypeOptions = []
				this.unitOptions = []
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await stockInfoApi.getUnitList({ pageNo: 1, pageSize: 100 })
				let units = []
				if (response && response.data && response.data.list) {
					units = response.data.list
				}

				this.unitOptions = units
			} catch (error) {
				this.unitOptions = []
			}
		},

		// 添加交易明细
		handleAdd() {
			const newTransaction = {
				id: undefined,
				bizId: undefined,
				bizNo: undefined,
				bizDetailId: undefined,
				transactionType: undefined,
				transactionDirection: undefined,
				materialId: undefined,
				materialCode: undefined,
				materialName: undefined,
				materialType: undefined,
				materialSpec: undefined,
				moveType: undefined,
				fromWarehouseId: undefined,
				fromWarehouseName: undefined,
				fromLocationId: undefined,
				fromLocationName: undefined,
				toWarehouseId: undefined,
				toWarehouseName: undefined,
				toLocationId: undefined,
				toLocationName: undefined,
				moveDate: undefined,
				sourceId: undefined,
				sourceNo: undefined,
				inventoryId: this.stockId,
				inventoryBatchNo: undefined,
				quantity: undefined,
				quantityUnit: undefined,
				auxiliaryQuantity: undefined,
				auxiliaryUnit: undefined,
				remark: undefined,
				beforeQuantity: undefined,
				afterQuantity: undefined,
				costObjectCode: undefined,
				costObjectName: undefined,
				accountingVoucherNumber: undefined,
				tenantName: undefined
			}
			this.transactionList.push(newTransaction)
		},

		// 删除交易明细
		handleDelete(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条交易明细吗？',
				success: (res) => {
					if (res.confirm) {
						this.transactionList.splice(index, 1)
					}
				}
			})
		},

		// 取消编辑
		handleCancel() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消编辑吗？未保存的修改将丢失。',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		},

		// 保存交易明细
		async handleSave() {
			try {
				this.saving = true

				// 这里可以添加表单验证逻辑
				// 验证必填字段等

				// 将交易明细数据传回上一页
				// 使用uni的事件通信机制
				uni.$emit('updateTransactionList', this.transactionList)

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)

			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				})
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.transaction-edit-container {
	height: 100vh;
	width: 100vw;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
}

.header {
	background-color: #ffffff;
	padding: 32rpx 40rpx;
	border-bottom: 1px solid #eee;

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		text-align: center;
	}
}

.form-content {
	flex: 1;
	padding: 32rpx 40rpx;
	padding-bottom: 140rpx; // 为底部按钮留出空间
}

.transaction-list {
	.transaction-item {
		background-color: #f8f9fa;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		border: 1px solid #e9ecef;

		.item-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 32rpx;
			padding-bottom: 16rpx;
			border-bottom: 1px solid #dee2e6;

			.item-index {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}

			.delete-btn {
				background-color: #dc3545;
				color: white;
				border: none;
				border-radius: 8rpx;
				padding: 12rpx 20rpx;
				font-size: 24rpx;
			}
		}

		.form-section {
			margin-bottom: 32rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.section-title {
				font-size: 28rpx;
				font-weight: 600;
				color: #495057;
				margin-bottom: 24rpx;
				padding-bottom: 12rpx;
				border-bottom: 1px solid #e9ecef;
			}
		}
	}
}

.add-section {
	text-align: center;
	margin-top: 32rpx;

	.add-btn {
		background-color: #28a745;
		color: white;
		border: none;
		border-radius: 12rpx;
		padding: 24rpx 48rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 32rpx 40rpx;
	border-top: 1px solid #eee;
	z-index: 100;
	display: flex;
	gap: 24rpx;

	.cancel-button {
		flex: 1;
		height: 88rpx;
		background-color: #6c757d;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-button {
		flex: 1;
		height: 88rpx;
		background-color: #007aff;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-button:disabled {
		background-color: #c0c4cc;
		color: #ffffff;
		cursor: not-allowed;
	}
}

.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
