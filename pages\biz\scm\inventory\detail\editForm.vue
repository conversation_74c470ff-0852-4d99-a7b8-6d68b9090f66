<template>
	<view class="receipt-detail-page">
		<!-- 主表单 -->
		<uv-form ref="formRef" :model="formModel" :rules="formValidationRules" labelPosition="top" labelWidth="120">
			<!-- 基本信息卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">基本信息</text>
				</view>
				<view class="card-content">
					<!-- 单号 -->
					<uv-form-item label="单号" prop="orderNo">
						<uv-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled></uv-input>
					</uv-form-item>

					<!-- 业务类型 -->
					<uv-form-item label="业务类型" prop="bizType">
						<picker
							mode="selector"
							:range="bizTypePickerOptions"
							range-key="text"
							:value="bizTypePickerIndex"
							@change="onBizTypeChange"
						>
							<view class="picker-input">
								<text :class="['picker-text', !bizTypeDisplay ? 'placeholder' : '']">
									{{ bizTypeDisplay || '请选择业务类型' }}
								</text>
								<text class="picker-icon selector-icon">▼</text>
							</view>
						</picker>
					</uv-form-item>

					<!-- 来源类型 -->
					<uv-form-item label="来源类型" prop="sourceType">
						<picker
							mode="selector"
							:range="sourceTypePickerOptions"
							range-key="text"
							:value="sourceTypePickerIndex"
							@change="onSourceTypeChange"
						>
							<view class="picker-input">
								<text :class="['picker-text', !sourceTypeDisplay ? 'placeholder' : '']">
									{{ sourceTypeDisplay || '请选择来源类型' }}
								</text>
								<text class="picker-icon selector-icon">▼</text>
							</view>
						</picker>
					</uv-form-item>

					<!-- 来源单编号 -->
					<uv-form-item label="来源单编号" prop="sourceNo">
						<picker
							mode="selector"
							:range="sourceOrderPickerOptions"
							range-key="text"
							:value="sourceOrderPickerIndex"
							@change="onSourceOrderChange"
						>
							<view class="picker-input">
								<text :class="['picker-text', !sourceOrderDisplay ? 'placeholder' : '']">
									{{ sourceOrderDisplay || '请选择来源单' }}
								</text>
								<text class="picker-icon selector-icon">▼</text>
							</view>
						</picker>
					</uv-form-item>

					<!-- 交易对象名称 -->
					<uv-form-item label="交易对象名称" prop="objectName">
						<uv-input v-model="formData.objectName" placeholder="请输入交易对象名称"></uv-input>
					</uv-form-item>

					<!-- 交易对象订单号 -->
					<uv-form-item label="交易对象订单号" prop="objectOrderNo">
						<uv-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号"></uv-input>
					</uv-form-item>

					<!-- 交易日期 -->
					<uv-form-item label="交易日期" prop="date">
						<uni-datetime-picker
							type="date"
							:value="tradeDateDisplay"
							@change="onTradeDateChange"
							placeholder="选择交易日期"
						/>
					</uv-form-item>

					<!-- 仓库名称 -->
					<uv-form-item label="仓库名称" prop="warehouseId">
						<uni-data-picker
							v-model="formData.warehouseId"
							:localdata="warehousePickerData"
							:map="warehousePickerMap"
							placeholder="请选择仓库"
							popup-title="选择仓库"
							:border="true"
							:clear-icon="true"
							@change="onWarehouseChange"
						></uni-data-picker>
					</uv-form-item>

					<!-- 摘要 -->
					<uv-form-item label="摘要" prop="note">
						<uv-input v-model="formData.note" placeholder="请输入摘要"></uv-input>
					</uv-form-item>

					<!-- 备注 -->
					<uv-form-item label="备注" prop="remark">
						<uv-input v-model="formData.remark" placeholder="请输入备注"></uv-input>
					</uv-form-item>
				</view>
			</view>

			<!-- 产品详情卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">{{ productCardTitle }}</text>
					<view class="add-product-btn" @click="editDetail">
						<text class="add-icon">+</text>
						<text class="add-text">添加产品</text>
					</view>
				</view>
				<view class="card-content">
					<view v-if="receiptDetails.length === 0" class="empty-products">
						<text class="empty-text">暂无产品，请点击上方按钮添加产品</text>
					</view>
					<view v-else class="product-list">
						<view
							v-for="(product, index) in receiptDetails"
							:key="index"
							class="product-item"
						>
							<!-- 产品基本信息 -->
							<view class="product-header">
								<view class="product-info-header">
									<view class="product-name">{{ product.materialName || '未知产品' }}</view>
									<view class="product-code">编码: {{ product.materialCode || '-' }}</view>
								</view>
								<view class="product-actions">
									<view class="action-btn edit-btn" @click="editDetail(product)">
										<text class="action-icon">✏️</text>
									</view>
									<view class="action-btn delete-btn" @click="deleteDetail(product)">
										<text class="action-icon">🗑️</text>
									</view>
								</view>
							</view>

							<!-- 产品详细信息 -->
							<view class="product-details">
								<!-- 核心信息 -->
								<view class="detail-section">
									<text class="section-title">核心信息</text>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">序号:</text>
											<text class="detail-value">{{ product.num || '-' }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">单号:</text>
											<text class="detail-value">{{ product.bizOrderNo || '-' }}</text>
										</view>
									</view>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">库位:</text>
											<text class="detail-value">{{ getLocationName(product.locationId) }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">单位:</text>
											<text class="detail-value">{{ getUnitName(product.unit) }}</text>
										</view>
									</view>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">计划数量:</text>
											<text class="detail-value important">{{ formatNumber(product.plannedQuantity) || 0 }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">完成数量:</text>
											<text class="detail-value important">{{ formatNumber(product.fulfilledQuantity) || 0 }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.standardPlannedQuantity || product.standardFulfilledQuantity">
										<view class="detail-item">
											<text class="detail-label">基本单位计划数量:</text>
											<text class="detail-value">{{ formatNumber(product.standardPlannedQuantity) || 0 }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">基本单位完成数量:</text>
											<text class="detail-value">{{ formatNumber(product.standardFulfilledQuantity) || 0 }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="getUnitName(product.standardUnit) !== '-'">
										<view class="detail-item">
											<text class="detail-label">基本单位:</text>
											<text class="detail-value">{{ getUnitName(product.standardUnit) }}</text>
										</view>
									</view>
								</view>

								<!-- 价格与开票信息 -->
								<view class="detail-section" v-if="product.unitPrice || product.amount || product.taxPrice || product.taxAmount || product.invoiceQuantity || product.invoiceAmount">
									<text class="section-title">价格与开票信息</text>
									<view class="detail-row" v-if="product.unitPrice || product.taxPrice">
										<view class="detail-item" v-if="product.unitPrice">
											<text class="detail-label">单价:</text>
											<text class="detail-value">{{ formatNumber(product.unitPrice) || 0 }}</text>
										</view>
										<view class="detail-item" v-if="product.taxPrice">
											<text class="detail-label">含税单价:</text>
											<text class="detail-value">{{ formatNumber(product.taxPrice) || 0 }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.amount || product.taxAmount">
										<view class="detail-item" v-if="product.amount">
											<text class="detail-label">金额:</text>
											<text class="detail-value important">{{ formatNumber(product.amount) || 0 }}</text>
										</view>
										<view class="detail-item" v-if="product.taxAmount">
											<text class="detail-label">含税金额:</text>
											<text class="detail-value important">{{ formatNumber(product.taxAmount) || 0 }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.invoiceQuantity || product.invoiceAmount">
										<view class="detail-item" v-if="product.invoiceQuantity">
											<text class="detail-label">开票数量:</text>
											<text class="detail-value">{{ formatNumber(product.invoiceQuantity) || 0 }}</text>
										</view>
										<view class="detail-item" v-if="product.invoiceAmount">
											<text class="detail-label">开票金额:</text>
											<text class="detail-value">{{ formatNumber(product.invoiceAmount) || 0 }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.standardInvoiceQuantity">
										<view class="detail-item">
											<text class="detail-label">开票基本数量:</text>
											<text class="detail-value">{{ formatNumber(product.standardInvoiceQuantity) || 0 }}</text>
										</view>
									</view>
								</view>

								<!-- 追溯信息 -->
								<view class="detail-section" v-if="product.sourceId || product.sourceNo || product.batchNo || product.effictiveDate || product.expiryDate">
									<text class="section-title">来源信息</text>
									<view class="detail-row" v-if="product.sourceId || product.sourceNo">
										<view class="detail-item" v-if="product.sourceId">
											<text class="detail-label">源单ID:</text>
											<text class="detail-value">{{ product.sourceId }}</text>
										</view>
										<view class="detail-item" v-if="product.sourceNo">
											<text class="detail-label">源单单号:</text>
											<text class="detail-value">{{ product.sourceNo }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.batchNo || product.effictiveDate">
										<view class="detail-item" v-if="product.batchNo">
											<text class="detail-label">批号:</text>
											<text class="detail-value">{{ product.batchNo }}</text>
										</view>
										<view class="detail-item" v-if="product.effictiveDate">
											<text class="detail-label">生产日期:</text>
											<text class="detail-value">{{ formatDate(product.effictiveDate) }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.expiryDate">
										<view class="detail-item">
											<text class="detail-label">失效日期:</text>
											<text class="detail-value">{{ formatDate(product.expiryDate) }}</text>
										</view>
									</view>
								</view>

								<!-- 成本与财务信息 -->
								<view class="detail-section" v-if="product.costObjectId || product.costObjectName || product.accountingVoucherNumber">
									<text class="section-title">成本与财务信息</text>
									<view class="detail-row" v-if="product.costObjectId">
										<view class="detail-item">
											<text class="detail-label">成本对象编码:</text>
											<text class="detail-value">{{ product.costObjectId }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.costObjectName">
										<view class="detail-item full-width">
											<text class="detail-label">成本对象名称:</text>
											<text class="detail-value">{{ product.costObjectName }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.accountingVoucherNumber">
										<view class="detail-item full-width">
											<text class="detail-label">记账凭证号:</text>
											<text class="detail-value">{{ product.accountingVoucherNumber }}</text>
										</view>
									</view>
								</view>

								<!-- 备注信息 -->
								<view class="detail-section" v-if="product.remark || product.note">
									<text class="section-title">备注信息</text>
									<view class="detail-row" v-if="product.remark">
										<view class="detail-item full-width">
											<text class="detail-label">备注:</text>
											<text class="detail-value">{{ product.remark }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="product.note">
										<view class="detail-item full-width">
											<text class="detail-label">说明:</text>
											<text class="detail-value">{{ product.note }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uv-form>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="cu-btn bg-blue lg round block" @click="submitForm" :disabled="submitLoading">
				<text v-if="submitLoading" class="cuIcon-loading2 cuIconfont-spin margin-right-sm"></text>
				<text v-if="submitLoading">提交中...</text>
				<text v-else>{{ isUpdate ? '更新' : '保存' }}</text>
			</button>
		</view>
	</view>
</template>

<script>
// 销售出库单API
import { getDeliveryReceiptApi, createDelieryReceiptApi, updateDeliveryReceiptApi, getDeliveryReceiptDetailListByBizOrderId } from '../../../../../api/scm/inventory/deliveryRecipt'
// 采购入库单API
import { getPurchaseReceiptApi, createPurchaseReceiptApi, updatePurchaseReceiptApi, getPurchaseReceiptDetailListByBizOrderId } from '../../../../../api/scm/inventory/purchaseReceipt'
// 销售订单API
import { getOrderPageApi, getOrderDetailListByOrderId } from '../../../../../api/scm/sale/order/index.js'
// 采购订单API
import { getPurchaseOrderPageApi, getPurchaseOrderDetailListByOrderId } from '../../../../../api/scm/purchase/order/index.js'
import { getWarehouseListApi } from '../../../../../api/scm/base/warehouse'
import { getUnitPageApi } from '../../../../../api/scm/base/unit'
import { getBatchDictOptions } from '../../../../../utils/dict.js'
import { handleTree } from '../../../../../utils/tree.js'

export default {
	data() {
		return {
			receiptId: null,
			isUpdate: false,
			submitLoading: false,
			receiptType: 'deliveryReceipt', // 默认为销售出库单，支持：deliveryReceipt, purchaseReceipt

			// 表单数据
			formData: {
				id: undefined,
				orderNo: undefined,
				bizType: undefined,
				sourceType: undefined,
				sourceNo: undefined,
				objectName: undefined,
				objectOrderNo: undefined,
				date: undefined,
				warehouseId: undefined,
				note: undefined,
				remark: undefined,
			},

			// 表单验证规则
			formRules: {
				// 暂时不添加验证规则
			},

			// 字典数据
			dictData: {},
			materialSourceOptions: [],
			bizTypeOptions: [],
			warehouseOptions: [],
			unitOptions: [],

			// 仓库树形数据
			warehouseTreeData: [],

			// 来源单数据
			sourceOrderOptions: [], // 来源单选项

			// 产品详情
			receiptDetails: [],
		}
	},

	computed: {
		pageTitle() {
			const typeMap = {
				'deliveryReceipt': this.isUpdate ? '编辑销售出库' : '新增销售出库',
				'purchaseReceipt': this.isUpdate ? '编辑采购入库' : '新增采购入库'
			}
			return typeMap[this.receiptType] || typeMap['deliveryReceipt']
		},

		// 产品详情卡片标题
		productCardTitle() {
			const typeMap = {
				'deliveryReceipt': '销售出库明细',
				'purchaseReceipt': '采购入库明细'
			}
			return typeMap[this.receiptType] || typeMap['deliveryReceipt']
		},

		// 表单模型 - 避免直接传递formData给UvForm组件
		formModel() {
			return { ...this.formData }
		},

		// 表单验证规则 - 避免直接传递formRules给UvForm组件
		formValidationRules() {
			return { ...this.formRules }
		},

		// 业务类型选择器选项
		bizTypePickerOptions() {
			return this.bizTypeOptions.map(item => ({
				text: item.label,
				value: item.value
			}))
		},

		bizTypePickerIndex() {
			return this.bizTypePickerOptions.findIndex(item => item.value === this.formData.bizType)
		},

		bizTypeDisplay() {
			const option = this.bizTypePickerOptions.find(item => item.value === this.formData.bizType)
			return option ? option.text : ''
		},

		// 来源类型选择器选项
		sourceTypePickerOptions() {
			return this.materialSourceOptions.map(item => ({
				text: item.label,
				value: item.value
			}))
		},

		sourceTypePickerIndex() {
			return this.sourceTypePickerOptions.findIndex(item => item.value === this.formData.sourceType)
		},

		sourceTypeDisplay() {
			const option = this.sourceTypePickerOptions.find(item => item.value === this.formData.sourceType)
			return option ? option.text : ''
		},

		// 来源单选择器选项
		sourceOrderPickerOptions() {
			return this.sourceOrderOptions.map(item => ({
				text: item.orderNo,
				value: item.orderNo,
				data: item
			}))
		},

		sourceOrderPickerIndex() {
			return this.sourceOrderPickerOptions.findIndex(item => item.value === this.formData.sourceNo)
		},

		sourceOrderDisplay() {
			const option = this.sourceOrderPickerOptions.find(item => item.value === this.formData.sourceNo)
			return option ? option.text : ''
		},

		// 日期显示
		tradeDateDisplay() {
			if (this.formData.date) {
				return this.formatDate(this.formData.date)
			}
			return ''
		},

		// 仓库选择器数据 - 使用计算属性避免prop mutation警告
		warehousePickerData() {
			return [...this.warehouseTreeData]
		},

		// 仓库选择器映射配置 - 使用计算属性避免prop mutation警告
		warehousePickerMap() {
			return {
				text: 'name',
				value: 'id'
			}
		}
	},

	async created() {
		// 初始化字典数据、仓库数据和单位数据
		await Promise.all([
			this.initDictData(),
			this.initWarehouseData(),
			this.initUnitData()
		])
		// 初始化来源单数据
		await this.initSourceOrderData()
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			try {
				const dictTypes = ['material_source', 'inventory_transaction_type']
				this.dictData = await getBatchDictOptions(dictTypes)

				this.materialSourceOptions = this.dictData.material_source || []
				this.bizTypeOptions = this.dictData.inventory_transaction_type || []
			} catch (error) {
				console.error('获取字典数据失败')
			}
		},

		// 初始化仓库数据
		async initWarehouseData() {
			try {
				const response = await getWarehouseListApi({
					pageNo: 1,
					pageSize: 100
				})

				if (response.code === 0) {
					const rawData = Array.isArray(response.data) ? response.data : []

					// 保留原始数据用于其他用途
					this.warehouseOptions = rawData
					// 转换为树形结构供uni-data-picker使用
					this.warehouseTreeData = this.convertToTreeData(rawData)
				} else {
					this.warehouseOptions = []
					this.warehouseTreeData = []
				}
			} catch (error) {
				this.warehouseOptions = []
				this.warehouseTreeData = []
			}
		},

		// 将仓库数据转换为树形结构
		convertToTreeData(data) {
			if (!Array.isArray(data) || data.length === 0) {
				return []
			}

			// 如果数据已经是树形结构，直接返回
			if (data.some(item => item.children && Array.isArray(item.children))) {
				return data
			}

			// 如果是扁平数据，转换为树形结构
			const treeData = handleTree(data, 'id', 'parentId', 'children')
			return treeData
		},

		// 初始化单位数据
		async initUnitData() {
			try {
				const response = await getUnitPageApi({
					pageNo: 1,
					pageSize: 100 // 获取所有单位数据
				})

				if (response.code === 0) {
					this.unitOptions = Array.isArray(response.data.list) ? response.data.list : []
				} else {
					this.unitOptions = []
				}
			} catch (error) {
				this.unitOptions = []
			}
		},

		// 初始化来源单数据
		async initSourceOrderData() {
			try {
				let response
				const params = {
					pageNo: 1,
					pageSize: 100,
					approveStatus: 'approved' // 只获取已审批的订单
				}

				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单 - 获取采购订单
					response = await getPurchaseOrderPageApi(params)
				} else {
					// 销售出库单 - 获取销售订单
					response = await getOrderPageApi(params)
				}

				if (response.code === 0) {
					this.sourceOrderOptions = Array.isArray(response.data.list) ? response.data.list : []
				} else {
					this.sourceOrderOptions = []
				}
			} catch (error) {
				console.error('获取来源单数据失败:', error)
				this.sourceOrderOptions = []
			}
		},

		// 初始化出库详情
		async initReceiptDetail(id) {
			try {
				const loadingTitle = this.receiptType === 'purchaseReceipt' ? '加载入库详情...' : '加载出库详情...'
				uni.showLoading({ title: loadingTitle })

				// 根据单据类型调用不同的API
				let receiptResponse, detailsResponse

				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单
					[receiptResponse, detailsResponse] = await Promise.all([
						getPurchaseReceiptApi(id),
						getPurchaseReceiptDetailListByBizOrderId(id)
					])
				} else {
					// 销售出库单（默认）
					[receiptResponse, detailsResponse] = await Promise.all([
						getDeliveryReceiptApi(id),
						getDeliveryReceiptDetailListByBizOrderId(id)
					])
				}


				// 处理主表数据
				if (receiptResponse.code !== 0) {
					uni.showToast({
						title: '获取销售出库详情失败',
						icon: 'error'
					})
					return
				}

				const receiptData = receiptResponse.data || {}

				// 设置表单数据
				this.formData = {
					...this.formData,
					...receiptData
				}

				// 处理明细数据
				if (detailsResponse.code === 0) {
					this.receiptDetails = Array.isArray(detailsResponse.data) ? detailsResponse.data : []
				} else {
					this.receiptDetails = []
				}
			} catch (error) {
				uni.showToast({
					title: '加载出库详情失败',
					icon: 'error'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 业务类型选择变化
		onBizTypeChange(e) {
			const index = e.detail.value
			if (index >= 0 && index < this.bizTypePickerOptions.length) {
				this.formData.bizType = this.bizTypePickerOptions[index].value
			}
		},

		// 来源类型选择变化
		onSourceTypeChange(e) {
			const index = e.detail.value
			if (index >= 0 && index < this.sourceTypePickerOptions.length) {
				this.formData.sourceType = this.sourceTypePickerOptions[index].value
				// 来源类型变化时，重新加载来源单数据
				this.initSourceOrderData()
			}
		},

		// 来源单选择变化
		async onSourceOrderChange(e) {
			const index = e.detail.value
			if (index >= 0 && index < this.sourceOrderPickerOptions.length) {
				const selectedOption = this.sourceOrderPickerOptions[index]
				const selectedOrder = selectedOption.data

				// 设置来源单编号
				this.formData.sourceNo = selectedOption.value

				// 自动填充相关字段
				if (selectedOrder) {
					// 填充交易对象信息
					if (this.receiptType === 'purchaseReceipt') {
						// 采购入库单 - 填充供应商信息
						this.formData.objectName = selectedOrder.supplierName || ''
					} else {
						// 销售出库单 - 填充客户信息
						this.formData.objectName = selectedOrder.customerName || ''
					}

					// 填充交易日期
					if (selectedOrder.orderDate) {
						this.formData.date = selectedOrder.orderDate
					}

					// 获取并替换订单明细
					await this.loadOrderDetails(selectedOrder.id)
				}
			}
		},

		// 加载订单明细数据
		async loadOrderDetails(orderId) {
			try {
				uni.showLoading({ title: '加载订单明细...' })

				let response
				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单 - 获取采购订单明细
					response = await getPurchaseOrderDetailListByOrderId(orderId)
				} else {
					// 销售出库单 - 获取销售订单明细
					response = await getOrderDetailListByOrderId({
						pageNo: 1,
						pageSize: 100 // 获取所有明细
					}, orderId)
				}

				if (response.code === 0) {
					let orderDetails = []

					if (this.receiptType === 'purchaseReceipt') {
						// 采购订单明细直接返回数组
						orderDetails = Array.isArray(response.data) ? response.data : []
					} else {
						// 销售订单明细返回分页数据
						orderDetails = Array.isArray(response.data.list) ? response.data.list : []
					}

					// 转换订单明细为出库/入库明细格式
					this.receiptDetails = this.convertOrderDetailsToReceiptDetails(orderDetails)

					uni.showToast({
						title: '订单明细加载成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: '获取订单明细失败',
						icon: 'error'
					})
				}
			} catch (error) {
				console.error('获取订单明细失败:', error)
				uni.showToast({
					title: '获取订单明细失败',
					icon: 'error'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 转换订单明细为出库/入库明细格式
		convertOrderDetailsToReceiptDetails(orderDetails) {
			return orderDetails.map((detail, index) => {
				let receiptDetail = {}

				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单数据结构
					receiptDetail = {
						// 基本信息
						num: index + 1, // 序号
						materialId: detail.materialId, // 物料ID
						materialCode: detail.materialCode, // 物料编码
						materialName: detail.materialName, // 物料名称
						materialSpec: detail.materialSpec || '', // 规格

						// 数量信息
						unit: detail.unit, // 单位ID
						plannedQuantity: detail.quantity || 0, // 订单数量作为应收数量
						fulfilledQuantity: 0, // 实收数量初始为0
						auxiliaryUnit: detail.auxilaryUnit, // 辅助单位ID
						auxiliaryPlannedQuantity: detail.auxilaryQuantity || 0, // 辅助单位应收数量
						auxiliaryFulfilledQuantity: 0, // 辅助单位实收数量初始为0

						// 价格信息
						unitPrice: detail.unitTaxPrice || detail.unitPrice || 0, // 含税单价
						price: detail.unitPrice || 0, // 不含税单价
						amount: detail.amount || 0, // 不含税金额
						taxPrice: detail.unitTaxPrice || detail.unitPrice || 0, // 含税单价
						taxAmount: detail.totalAmount || detail.taxAmount || 0, // 含税金额
						taxRate: detail.tax || 0, // 税率

						// 开票信息
						invoiceQuantity: detail.invoiceQuantity || 0,
						invoiceAmount: 0,
						auxiliaryInvoiceQuantity: detail.invoiceAuxilaryQuantity || 0,

						// 来源信息
						sourceOrderId: detail.orderId,
						sourceOrderDetailId: detail.id,
						sourceType: this.formData.sourceType,
						sourceNo: this.formData.sourceNo,

						// 其他信息
						remark: detail.remark || '',
						batchNo: detail.batchNo || '', // 批次号
						receivedDate: detail.receivedDate, // 收货日期

						// 采购入库单特有字段
						supplierName: this.formData.objectName,

						// 临时ID用于前端操作
						tempId: Date.now() + Math.random() + index
					}
				} else {
					// 销售出库单数据结构
					receiptDetail = {
						// 基本信息
						num: index + 1, // 序号
						materialId: detail.productId, // 产品ID
						materialCode: detail.productCode, // 产品编码
						materialName: detail.productName, // 产品名称
						materialSpec: detail.spec, // 规格

						// 数量信息
						unit: detail.unit, // 单位ID
						plannedQuantity: detail.quantity || 0, // 订单数量作为应发数量
						fulfilledQuantity: 0, // 实发数量初始为0
						standardUnit: detail.standardUnitId, // 基本单位ID
						standardPlannedQuantity: detail.standardQuantity || 0, // 基本单位数量
						standardFulfilledQuantity: 0,

						// 价格信息
						unitPrice: detail.unitPrice || 0, // 含税单价
						price: detail.noTaxUnitPrice || 0, // 不含税单价
						amount: detail.noTaxTotal || 0, // 不含税金额
						taxPrice: detail.unitPrice || 0, // 含税单价
						taxAmount: detail.total || 0, // 含税金额
						taxRate: detail.taxRate || 0, // 税率

						// 开票信息
						invoiceQuantity: 0,
						invoiceAmount: 0,
						standardInvoiceQuantity: 0,

						// 来源信息
						sourceOrderId: detail.orderId,
						sourceOrderDetailId: detail.id,
						sourceType: this.formData.sourceType,
						sourceNo: this.formData.sourceNo,

						// 其他信息
						remark: detail.remark || '',
						requirement: detail.requirement || '', // 订单要求
						deliveryDate: detail.deliveryDate, // 交货日期

						// 销售出库单特有字段
						customerName: this.formData.objectName,

						// 临时ID用于前端操作
						tempId: Date.now() + Math.random() + index
					}
				}

				return receiptDetail
			})
		},

		// 仓库选择变化
		onWarehouseChange(e) {
			// uni-data-picker的change事件返回格式：{detail: {value: [...选中路径]}}
			let selectedValue = null

			if (e && e.detail && e.detail.value) {
				// 从detail.value数组中获取最后一个选中的节点（叶子节点）
				const selectedPath = e.detail.value
				if (Array.isArray(selectedPath) && selectedPath.length > 0) {
					const lastNode = selectedPath[selectedPath.length - 1]
					selectedValue = lastNode.value || lastNode.id
				}
			}
			// 兼容其他可能的数据格式
			else if (e) {
				// 如果e是数组（树形选择的情况）
				if (Array.isArray(e) && e.length > 0) {
					const selectedNode = e[e.length - 1] // 获取最后一个选中的节点
					selectedValue = selectedNode.value || selectedNode.id
				}
				// 如果e是对象（直接选择的情况）
				else if (typeof e === 'object' && (e.value || e.id)) {
					selectedValue = e.value || e.id
				}
				// 如果e是简单值
				else if (typeof e === 'string' || typeof e === 'number') {
					selectedValue = e
				}
			}

			this.formData.warehouseId = selectedValue
		},

		// 交易日期变化
		onTradeDateChange(e) {
			const date = new Date(e.replace(/-/g, '/'))
			this.formData.date = date.getTime()
		},

		// 格式化日期 - 只显示年月日
		formatDate(dateStr) {
			if (!dateStr) return ''
			const date = new Date(dateStr)
			if (isNaN(date.getTime())) return ''

			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')

			return `${year}-${month}-${day}`
		},

		// 格式化数字
		formatNumber(value) {
			if (value === null || value === undefined || value === '') return ''
			const num = parseFloat(value)
			return isNaN(num) ? '' : num.toFixed(2)
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId || !this.unitOptions.length) return '-'
			const unit = this.unitOptions.find(item => item.id === unitId)
			return unit ? unit.name : unitId.toString()
		},

		// 获取库位名称
		getLocationName(locationId) {
			if (!locationId) return '-'

			// 从扁平的仓库选项中查找库位
			const findLocationInTree = (nodes, targetId) => {
				for (const node of nodes) {
					if (node.id === targetId) {
						return node.name
					}
					if (node.children && node.children.length > 0) {
						const found = findLocationInTree(node.children, targetId)
						if (found) return found
					}
				}
				return null
			}

			const locationName = findLocationInTree(this.warehouseTreeData, locationId)
			return locationName || locationId.toString()
		},

		// 编辑明细信息
		editDetail(product) {
			let isUpdate = false
			let detailForm  = {}

			// 如果传入了product参数，说明是编辑模式
			if(product && (product.id || product.tempId || product.materialId)){
				isUpdate = true
				detailForm = { ...product } // 使用展开运算符避免引用问题
			}

			uni.navigateTo({
				url:'/pages/biz/scm/inventory/detail/editFormDetail',
				success:(res) => {
					res.eventChannel.emit('acceptDataFormOpener',{
						detailForm,
						isUpdate
					})

					// 监听明细保存事件
					res.eventChannel.on('saveFormDetail', (data) => {
						this.handleSaveFormDetail(data)
					})
				}
			})
		},

		// 处理明细保存
		handleSaveFormDetail(data) {
			try {
				const { formDetail, isUpdate } = data

				if (isUpdate) {
					// 更新现有明细
					const index = this.receiptDetails.findIndex(item => {
						// 如果有ID，按ID匹配；否则按对象引用匹配
						if (formDetail.id && item.id) {
							return item.id === formDetail.id
						}
						// 如果没有ID，可以按物料ID和其他关键字段匹配
						return item.materialId === formDetail.materialId &&
							   item.num === formDetail.num
					})

					if (index !== -1) {
						// 更新现有项
						this.receiptDetails.splice(index, 1, { ...formDetail })
					} else {
						// 如果找不到匹配项，作为新增处理
						this.receiptDetails.push({ ...formDetail })
					}
				} else {
					// 新增明细
					// 为新增的明细生成临时ID
					const tempId = Date.now() + Math.random()
					const newDetail = {
						...formDetail,
						tempId: tempId,
						// 如果没有序号，自动生成
						num: formDetail.num || (this.receiptDetails.length + 1)
					}
					this.receiptDetails.push(newDetail)
				}

				// 触发页面更新
				this.$forceUpdate()

			} catch (error) {
				console.error('处理明细保存失败:', error)
				uni.showToast({
					title: '保存明细失败',
					icon: 'none'
				})
			}
		},

		// 删除产品
		deleteDetail(product) {
			uni.showModal({
				title: '提示',
				content: `确定要删除产品 "${product.materialName || '未知产品'}" 吗？`,
				cancelText: '取消',
				confirmText: '确定',
				success: (res) => {
					if (res.confirm) {
						try {
							// 从数组中删除该产品
							this.receiptDetails = this.receiptDetails.filter(item => {
								// 如果有ID，按ID比较；否则按索引比较
								if (product.id && item.id) {
									return item.id !== product.id
								} else {
									// 如果没有ID，按对象引用比较
									return item !== product
								}
							})

							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						} catch (error) {
							uni.showToast({
								title: '删除失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},

		// 提交表单
		async submitForm() {
			try {
				this.submitLoading = true

				// 判断是创建还是更新：有receiptId且formData有id字段则为更新
				const isUpdateOperation = this.receiptId && this.formData.id

				// 准备提交数据，包含主表单和明细数据
				let submitData
				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单数据结构
					submitData = {
						...this.formData,
						purchaseReceiptDetails: this.receiptDetails
					}
				} else {
					// 销售出库单数据结构（默认）
					submitData = {
						...this.formData,
						deliveryReceiptDetails: this.receiptDetails
					}
				}

				let response
				if (this.receiptType === 'purchaseReceipt') {
					// 采购入库单API调用
					if (isUpdateOperation) {
						response = await updatePurchaseReceiptApi(JSON.stringify(submitData))
					} else {
						response = await createPurchaseReceiptApi(JSON.stringify(submitData))
					}
				} else {
					// 销售出库单API调用（默认）
					if (isUpdateOperation) {
						response = await updateDeliveryReceiptApi(JSON.stringify(submitData))
					} else {
						response = await createDelieryReceiptApi(JSON.stringify(submitData))
					}
				}

				if (response.code === 0) {
					uni.showToast({
						title: isUpdateOperation ? '更新成功' : '保存成功',
						icon: 'success'
					})

					// 先通知父页面刷新，再返回
					setTimeout(() => {
						// 通知父页面刷新
						const pages = getCurrentPages()
						if (pages.length > 1) {
							const prevPage = pages[pages.length - 2]
							if (prevPage && prevPage.refreshList) {
								prevPage.refreshList()
							}
						}

						// 然后返回上一页
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: response.msg || '操作失败',
						icon: 'error'
					})
				}
			} catch (error) {
				uni.showToast({
					title: '提交失败',
					icon: 'error'
				})
			} finally {
				this.submitLoading = false
			}
		}
	},

	onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if (!eventChannel) {
			return
		}
		eventChannel.on('acceptDataFormOpener', (data) => {
			if (data) {
				this.receiptId = data.receiptId
				this.isUpdate = data.isUpdate
				// 设置单据类型，默认为销售出库单
				this.receiptType = data.receiptType || 'deliveryReceipt'
			}
			if (this.receiptId) {
				this.initReceiptDetail(this.receiptId)
			}
		})
	}
}
</script>

<style scoped>
.receipt-detail-page {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 24rpx;
	padding-bottom: 160rpx;
	box-sizing: border-box;
}

/* 卡片样式 */
.form-card {
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 20rpx 16rpx;
	border-bottom: 1px solid #f0f0f0;
}

.card-content {
	padding: 20rpx;
}

.card-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #2c3e50;
	flex: 1;
}

/* Picker输入框样式 - 与uv-input保持一致 */
.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
	background-color: #fff;
	border: 1px solid #dadbde;
	border-radius: 6rpx;
	min-height: 70rpx;
	box-sizing: border-box;
	font-size: 28rpx;
	transition: border-color 0.2s ease;
}

.picker-input:active {
	border-color: #3c9cff;
}

.picker-text {
	flex: 1;
	font-size: 28rpx;
	color: #303133;
	line-height: 1.4;
}

/* 占位符样式 */
.picker-text.placeholder {
	color: #c0c4cc;
}

/* 图标样式 */
.picker-icon {
	font-size: 24rpx;
	color: #909399;
	margin-left: 12rpx;
}

.selector-icon {
	font-size: 20rpx;
	transform: rotate(0deg);
	transition: transform 0.2s ease;
}

/* uni-data-picker 样式调整 - 与其他选择器保持一致 */
:deep(.uni-data-tree .input-value) {
	border: 1px solid #dadbde !important;
	border-radius: 6rpx !important;
	min-height: 70rpx !important;
	padding: 0 24rpx !important;
	font-size: 28rpx !important;
	background-color: #fff !important;
	box-sizing: border-box !important;
	transition: border-color 0.2s ease !important;
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
}

:deep(.uni-data-tree .input-value:active) {
	border-color: #3c9cff !important;
}

/* 选中文本样式 */
:deep(.uni-data-tree .selected-area) {
	color: #303133 !important;
	font-size: 28rpx !important;
	line-height: 1.4 !important;
	flex: 1 !important;
}

:deep(.uni-data-tree .selected-area .text-color) {
	color: #303133 !important;
}

/* 占位符样式 */
:deep(.uni-data-tree .input-value .placeholder-class) {
	color: #c0c4cc !important;
	font-size: 28rpx !important;
}

/* 下拉箭头样式 */
:deep(.uni-data-tree .arrow) {
	font-size: 20rpx !important;
	color: #909399 !important;
	margin-left: 12rpx !important;
	transition: transform 0.2s ease !important;
}

/* 确保整体容器样式一致 */
:deep(.uni-data-tree) {
	width: 100% !important;
}

:deep(.uni-data-tree .uni-data-tree-input) {
	width: 100% !important;
}

/* 底部操作按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1px solid #e8e8e8;
	width: 100%;
	height: 120rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
	box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.1);
	padding: 20rpx;
	box-sizing: border-box;
}

/* ColorUI按钮增强样式 */
.cu-btn.lg {
	padding: 0 40rpx;
	font-size: 32rpx;
	height: 88rpx;
	line-height: 88rpx;
}

.cu-btn.round {
	border-radius: 50rpx;
}

.cu-btn.block {
	width: 100%;
	display: flex;
}

.cu-btn.bg-blue {
	background-color: #007aff;
	color: white;
	border: none;
}

/* 加载动画 */
.cuIconfont-spin {
	animation: cuIcon-spin 2s infinite linear;
}

@keyframes cuIcon-spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 间距样式 */
.margin-right-sm {
	margin-right: 16rpx;
}

/* 表单分组样式 */
.form-section {
	margin-bottom: 32rpx;
}

.form-section:last-child {
	margin-bottom: 0;
}

.section-header {
	margin-bottom: 16rpx;
	padding: 0 4rpx;
}

.section-header .section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #2c3e50;
	position: relative;
	padding-left: 16rpx;
}

.section-header .section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 6rpx;
	height: 28rpx;
	background-color: #3c9cff;
	border-radius: 3rpx;
}

/* 添加产品按钮 */
.add-product-btn {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 20rpx;
	font-size: 26rpx;
	line-height: 1;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
	transition: all 0.2s ease;
	cursor: pointer;
}

.add-product-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.add-icon {
	margin-right: 8rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.add-text {
	font-weight: 500;
}

/* 产品列表样式 */
.empty-products {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 80rpx 0;
	color: #999;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
	margin-top: 16rpx;
}

.product-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.product-item {
	background-color: #f9f9f9;
	border-radius: 12rpx;
	padding: 24rpx;
	border: 1px solid #e8e8e8;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

/* 产品头部信息 */
.product-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1px solid #e8e8e8;
}

.product-info-header {
	flex: 1;
	min-width: 0; /* 允许flex子项收缩 */
	margin-right: 16rpx; /* 与操作按钮保持间距 */
}

.product-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #2c3e50;
	line-height: 1.4;
	margin-bottom: 8rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.product-code {
	font-size: 26rpx;
	color: #7f8c8d;
	line-height: 1.3;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 产品操作按钮 */
.product-actions {
	display: flex;
	gap: 12rpx;
	flex-shrink: 0; /* 防止按钮被压缩 */
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	transition: all 0.2s ease;
	cursor: pointer;
}

.action-btn:active {
	transform: scale(0.9);
}

.edit-btn {
	background-color: #f8f9fa;
	border: 1px solid #e9ecef;
}

.edit-btn:active {
	background-color: #e9ecef;
}

.delete-btn {
	background-color: #f8f9fa;
	border: 1px solid #e9ecef;
}

.delete-btn:active {
	background-color: #f5c6cb;
}

.action-icon {
	font-size: 28rpx;
}

/* 产品详细信息 */
.product-details {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 详情分组 */
.detail-section {
	background-color: #fafafa;
	border-radius: 8rpx;
	padding: 16rpx;
	border: 1px solid #f0f0f0;
}

.section-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #2c3e50;
	margin-bottom: 12rpx;
	display: block;
	padding-bottom: 8rpx;
	border-bottom: 1px solid #e8e8e8;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 12rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.detail-item.full-width {
	flex: none;
	width: 100%;
}

.detail-label {
	font-size: 24rpx;
	color: #7f8c8d;
	line-height: 1.2;
}

.detail-value {
	font-size: 28rpx;
	color: #2c3e50;
	line-height: 1.3;
	font-weight: 400;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 重要信息突出显示 */
.detail-value.important {
	font-weight: bold;
	color: #2c3e50;
}
</style>
