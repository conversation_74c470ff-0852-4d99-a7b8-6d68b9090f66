<template>
	<view class="edit-container">
		<!-- 表单内容 -->
		<scroll-view scroll-y class="form-content" v-if="formData">
			<uv-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-position="top"
				label-width="120"
			>
				<!-- 物料信息 -->
				<uv-form-item label="物料名称">
					<uv-input
						v-model="formData.materialName"
						placeholder="物料名称"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料编码">
					<uv-input
						v-model="formData.materialCode"
						placeholder="物料编码"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料规格">
					<uv-input
						v-model="formData.spec"
						placeholder="物料规格"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料类型">
					<uv-input
						:value="getMaterialTypeText(formData.materialType)"
						placeholder="物料类型"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料来源">
					<uv-input
						:value="getMaterialSourceText(formData.materialSource)"
						placeholder="物料来源"
						disabled
					/>
				</uv-form-item>

				<!-- 库存信息 -->
				<uv-form-item label="库存数量">
					<uv-input
						:value="formatQuantity(formData.quantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="库存数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="锁定数量">
					<uv-input
						:value="formatQuantity(formData.lockQuantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="锁定数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="未锁数量">
					<uv-input
						:value="formatQuantity(formData.unlockQuantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="可用数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="数量单位">
					<uv-input
						:value="getUnitName(formData.quantityUnit)"
						placeholder="数量单位"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="基本单位">
					<uv-input
						:value="getUnitName(formData.auxiliaryUnit)"
						placeholder="基本单位"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="状态">
					<uv-input
						:value="getStatusText(formData.status)"
						placeholder="状态"
						disabled
					/>
				</uv-form-item>

				<!-- 可编辑字段 -->
				<uv-form-item label="基本单位数量" prop="auxiliaryQuantity">
					<uv-input
						v-model="formData.auxiliaryQuantity"
						type="number"
						placeholder="请输入基本单位数量"
					/>
				</uv-form-item>

				<uv-form-item label="库存数量" prop="inventoryQuantity">
					<uv-input
						v-model="formData.inventoryQuantity"
						type="number"
						placeholder="请输入库存数量"
					/>
				</uv-form-item>

				<uv-form-item label="价格" prop="price">
					<uv-input
						v-model="formData.price"
						type="number"
						placeholder="请输入价格"
					/>
				</uv-form-item>

				<uv-form-item label="价格单位" prop="priceUnit">
					<SelectPicker
						v-model="formData.priceUnit"
						:options="unitOptions || []"
						title="选择价格单位"
						label-field="name"
						value-field="id"
						placeholder="请选择价格单位"
					/>
				</uv-form-item>

				<uv-form-item label="总价值" prop="totalCost">
					<uv-input
						v-model="formData.totalCost"
						type="number"
						placeholder="请输入总价值"
					/>
				</uv-form-item>

				<uv-form-item label="采购价格" prop="purchasePrice">
					<uv-input
						v-model="formData.purchasePrice"
						type="number"
						placeholder="请输入采购价格"
					/>
				</uv-form-item>

				<uv-form-item label="销售价格" prop="salePrice">
					<uv-input
						v-model="formData.salePrice"
						type="number"
						placeholder="请输入销售价格"
					/>
				</uv-form-item>

				<uv-form-item label="入库日期" prop="inDate">
					<uni-datetime-picker
						v-model="formData.inDate"
						type="date"
						placeholder="请选择入库日期"
						:clear-icon="false"
					/>
				</uv-form-item>

				<uv-form-item label="备注" prop="remark">
					<uv-input
						v-model="formData.remark"
						type="textarea"
						placeholder="请输入备注"
						maxlength="200"
						:autoHeight="true"
					/>
				</uv-form-item>

				<!-- 仓库信息 -->
				<uv-form-item label="仓库名称" prop="warehouseId">
					<uni-data-picker
						v-model="formData.warehouseId"
						:localdata="warehouseTreeData || []"
						popup-title="选择仓库"
						placeholder="请选择仓库"
						:map="{text: 'name', value: 'id'}"
						@change="handleWarehouseChange"
					/>
				</uv-form-item>

				<uv-form-item label="仓位名称" prop="locationId">
					<SelectPicker
						v-model="formData.locationId"
						:options="locationOptions || []"
						title="选择仓位"
						label-field="name"
						value-field="id"
						placeholder="请选择仓位"
					/>
				</uv-form-item>
			</uv-form>

			<!-- 库存交易明细 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">库存交易明细</text>
					<view class="add-transaction-btn" @click="editTransaction">
						<text class="add-icon">+</text>
						<text class="add-text">添加明细</text>
					</view>
				</view>
				<view class="card-content">
					<view v-if="transactionList.length === 0" class="empty-transactions">
						<text class="empty-text">暂无交易明细，请点击上方按钮添加明细</text>
					</view>
					<view v-else class="transaction-list">
						<view
							v-for="(transaction, index) in transactionList"
							:key="index"
							class="transaction-item"
						>
							<!-- 交易基本信息 -->
							<view class="transaction-header">
								<view class="transaction-info-header">
									<view class="transaction-type">{{ getTransactionTypeText(transaction.transactionType) }} - {{ getTransactionDirectionText(transaction.transactionDirection) }}</view>
									<view class="transaction-no">单号: {{ transaction.bizNo || transaction.documentNo || '-' }}</view>
								</view>
								<view class="transaction-actions">
									<view class="action-btn edit-btn" @click="editTransaction(transaction)">
										<text class="action-icon">✏️</text>
									</view>
									<view class="action-btn delete-btn" @click="deleteTransaction(transaction)">
										<text class="action-icon">🗑️</text>
									</view>
								</view>
							</view>

							<!-- 交易详细信息 -->
							<view class="transaction-details">
								<!-- 基本信息 -->
								<view class="detail-section">
									<text class="section-title">基本信息</text>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">交易编号:</text>
											<text class="detail-value">{{ transaction.bizId || '-' }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">移动日期:</text>
											<text class="detail-value">{{ formatDate(transaction.moveDate || transaction.createTime) }}</text>
										</view>
									</view>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">移动类型:</text>
											<text class="detail-value">{{ getMoveTypeText(transaction.moveType) }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">批号:</text>
											<text class="detail-value">{{ transaction.inventoryBatchNo || '-' }}</text>
										</view>
									</view>
								</view>

								<!-- 数量信息 -->
								<view class="detail-section">
									<text class="section-title">数量信息</text>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">数量:</text>
											<text class="detail-value important">{{ formatQuantity(transaction.quantity) }} {{ getUnitName(transaction.quantityUnit) }}</text>
										</view>
										<view class="detail-item" v-if="transaction.lockQuantity">
											<text class="detail-label">锁定数量:</text>
											<text class="detail-value">{{ formatQuantity(transaction.lockQuantity) }} {{ getUnitName(transaction.quantityUnit) }}</text>
										</view>
									</view>
									<view class="detail-row">
										<view class="detail-item">
											<text class="detail-label">出入库前:</text>
											<text class="detail-value">{{ formatQuantity(transaction.beforeQuantity) }} {{ getUnitName(transaction.quantityUnit) }}</text>
										</view>
										<view class="detail-item">
											<text class="detail-label">出入库后:</text>
											<text class="detail-value">{{ formatQuantity(transaction.afterQuantity) }} {{ getUnitName(transaction.quantityUnit) }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="transaction.auxiliaryQuantity">
										<view class="detail-item">
											<text class="detail-label">基本单位数量:</text>
											<text class="detail-value">{{ formatQuantity(transaction.auxiliaryQuantity) }} {{ getUnitName(transaction.auxiliaryUnit) }}</text>
										</view>
									</view>
								</view>

								<!-- 仓库信息 -->
								<view class="detail-section" v-if="transaction.fromWarehouseName || transaction.toWarehouseName">
									<text class="section-title">仓库信息</text>
									<view class="detail-row" v-if="transaction.fromWarehouseName || transaction.fromLocationName">
										<view class="detail-item" v-if="transaction.fromWarehouseName">
											<text class="detail-label">来源仓库:</text>
											<text class="detail-value">{{ transaction.fromWarehouseName }}</text>
										</view>
										<view class="detail-item" v-if="transaction.fromLocationName">
											<text class="detail-label">来源仓位:</text>
											<text class="detail-value">{{ transaction.fromLocationName }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="transaction.toWarehouseName || transaction.toLocationName">
										<view class="detail-item" v-if="transaction.toWarehouseName">
											<text class="detail-label">目标仓库:</text>
											<text class="detail-value">{{ transaction.toWarehouseName }}</text>
										</view>
										<view class="detail-item" v-if="transaction.toLocationName">
											<text class="detail-label">目标仓位:</text>
											<text class="detail-value">{{ transaction.toLocationName }}</text>
										</view>
									</view>
								</view>

								<!-- 成本与财务信息 -->
								<view class="detail-section" v-if="transaction.costObjectCode || transaction.costObjectName || transaction.accountingVoucherNumber">
									<text class="section-title">成本与财务信息</text>
									<view class="detail-row" v-if="transaction.costObjectCode">
										<view class="detail-item">
											<text class="detail-label">成本对象编码:</text>
											<text class="detail-value">{{ transaction.costObjectCode }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="transaction.costObjectName">
										<view class="detail-item full-width">
											<text class="detail-label">成本对象名称:</text>
											<text class="detail-value">{{ transaction.costObjectName }}</text>
										</view>
									</view>
									<view class="detail-row" v-if="transaction.accountingVoucherNumber">
										<view class="detail-item full-width">
											<text class="detail-label">记账凭证号:</text>
											<text class="detail-value">{{ transaction.accountingVoucherNumber }}</text>
										</view>
									</view>
								</view>

								<!-- 备注信息 -->
								<view class="detail-section" v-if="transaction.remark">
									<text class="section-title">备注信息</text>
									<view class="detail-row">
										<view class="detail-item full-width">
											<text class="detail-label">备注:</text>
											<text class="detail-value">{{ transaction.remark }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions" v-if="formData">
			<button
				class="save-button"
				:disabled="saving"
				@click="handleSave"
			>
				<text v-if="saving">保存中...</text>
				<text v-else>保存</text>
			</button>
		</view>

		<!-- 空数据状态 -->
		<view v-if="!loading && !formData && stockId" class="empty-container">
			<view class="empty-content">
				<text class="empty-text">暂无库存信息</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { stockInfoApi } from '@/api/scm/inventory/stockInfo/index.js'
import { getBatchDictOptions, DICT_TYPE } from '@/utils/dict.js'
import { getWarehouseListApi } from '@/api/scm/base/warehouse/index.js'
import { getWarehouseLocationPageApi } from '@/api/scm/inventory/warehouseLocation/index.js'
import SelectPicker from '@/components/SelectPicker/SelectPicker.vue'
import { handleTree } from '../../../../../utils/tree'

export default {
	components: {
		SelectPicker
	},
	data() {
		return {
			stockId: null,
			loading: false,
			saving: false,
			formData: null,
			transactionList: [],
			// 字典数据
			materialTypeOptions: [],
			materialSourceOptions: [],
			statusOptions: [],
			unitOptions: [],
			dictData: {},
			// 仓库和仓位数据
			warehouseTreeData: [],
			locationOptions: [],
			// 表单验证规则
			formRules: {
				auxiliaryQuantity: [
					{
						type: 'number',
						message: '请输入有效的数字',
						trigger: ['blur', 'change']
					}
				],
				inventoryQuantity: [
					{
						type: 'number',
						message: '请输入有效的数字',
						trigger: ['blur', 'change']
					}
				],
				price: [
					{
						type: 'number',
						message: '请输入有效的价格',
						trigger: ['blur', 'change']
					}
				],
				totalCost: [
					{
						type: 'number',
						message: '请输入有效的总价值',
						trigger: ['blur', 'change']
					}
				],
				purchasePrice: [
					{
						type: 'number',
						message: '请输入有效的采购价格',
						trigger: ['blur', 'change']
					}
				],
				salePrice: [
					{
						type: 'number',
						message: '请输入有效的销售价格',
						trigger: ['blur', 'change']
					}
				],
				remark: [
					{
						max: 200,
						message: '备注不能超过200个字符',
						trigger: ['blur', 'change']
					}
				]
			}
		}
	},
	async onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if(eventChannel){
			eventChannel.on('acceptDataFormOpener',(data) => {
				if(data) {
					this.stockId = data.stockId
					// 获取到stockId后立即初始化数据
					this.initData()
				}
			})
		}
	},

	onShow() {
		// 从交易明细编辑页面返回时，重新加载交易明细数据
		if (this.stockId) {
			this.loadTransactionData()
		}
	},
	methods: {

		// 初始化数据
		async initData() {
			await this.getDictData()
			await this.loadWarehouses()
			this.loadData()
		},

		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.MATERIAL_TYPE,
					DICT_TYPE.MATERIAL_SOURCE,
					DICT_TYPE.STOCK_STATUS,
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.materialTypeOptions = this.dictData[DICT_TYPE.MATERIAL_TYPE] || []
				this.materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
				this.statusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []

				// 加载单位数据
				await this.loadUnits()

			} catch (error) {
				// 字典数据加载失败不影响主要功能，只是显示可能不够友好
				// 设置默认空数组避免后续处理出错
				this.dictData = {}
				this.materialTypeOptions = []
				this.materialSourceOptions = []
				this.statusOptions = []
				this.unitOptions = []
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await stockInfoApi.getUnitList({ pageNo: 1, pageSize: 100 })
				let units = []
				if (response && response.data && response.data.list) {
					units = response.data.list
				}

				this.unitOptions = units
			} catch (error) {
				this.unitOptions = []
			}
		},

		// 加载仓库数据
		async loadWarehouses() {
			try {
				const response = await getWarehouseListApi({ pageNo: 1, pageSize: 100 })
				let warehouses = []
				if (response && response.data) {
					warehouses = handleTree(response.data, 'id', 'parentId')
				}

				this.warehouseTreeData = warehouses
			} catch (error) {
				this.warehouseTreeData = []
			}
		},

		// 加载仓位数据
		async loadLocations(warehouseId) {
			try {
				const params = { pageNo: 1, pageSize: 100 }
				if (warehouseId) {
					params.warehouseId = warehouseId
				}

				const response = await getWarehouseLocationPageApi(params)
				let locations = []
				if (response && response.data && response.data.list) {
					locations = response.data.list
				} else if (response && response.data && Array.isArray(response.data)) {
					locations = response.data
				} else if (Array.isArray(response)) {
					locations = response
				}

				this.locationOptions = locations
			} catch (error) {
				this.locationOptions = []
			}
		},

		// 仓库变化处理
		handleWarehouseChange(e) {
			const warehouseId = e.detail ? e.detail.value : e
			// 清空仓位选择
			this.formData.locationId = null
			// 加载对应仓库的仓位
			if (warehouseId) {
				this.loadLocations(warehouseId)
			} else {
				this.locationOptions = []
			}
		},

		// 加载数据
		async loadData() {
			if (!this.stockId) {
				return
			}

			this.loading = true
			try {

				// 加载库存信息
				const stockInfoResponse = await stockInfoApi.getStockInfo(this.stockId)


				// 处理API响应数据结构
				let stockInfo = null
				if (stockInfoResponse && stockInfoResponse.data) {
					stockInfo = stockInfoResponse.data
				} else if (stockInfoResponse && typeof stockInfoResponse === 'object') {
					stockInfo = stockInfoResponse
				}

				if (!stockInfo) {
					throw new Error('库存信息不存在或数据格式错误')
				}

				this.formData = stockInfo

				// 如果有仓库ID，加载对应的仓位数据
				if (stockInfo.warehouseId) {
					await this.loadLocations(stockInfo.warehouseId)
				}

				// 加载交易明细
				await this.loadTransactionData()

			} catch (error) {
				this.formData = null
				uni.showToast({
					title: error.message || '加载数据失败',
					icon: 'error',
					duration: 2000
				})

				// 如果是数据不存在，延迟返回上一页
				if (error.message === '库存信息不存在') {
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				}
			} finally {
				this.loading = false
			}
		},

		// 加载交易明细数据
		async loadTransactionData() {
			try {
				const transactionResponse = await stockInfoApi.getTransactionListByInventoryId(this.stockId)

				let transactions = []
				if (transactionResponse && transactionResponse.data) {
					transactions = transactionResponse.data
				} else if (Array.isArray(transactionResponse)) {
					transactions = transactionResponse
				}

				this.transactionList = transactions
			} catch (transactionError) {
				this.transactionList = []
			}
		},

		// 编辑交易明细
		editTransaction(transaction) {
			let isUpdate = false
			let transactionForm = {}

			// 如果传入了transaction参数，说明是编辑模式
			if(transaction && (transaction.id || transaction.tempId)){
				isUpdate = true
				transactionForm = { ...transaction } // 使用展开运算符避免引用问题
			}

			uni.navigateTo({
				url: '/pages/biz/scm/inventory/stockInfo/transactionEdit',
				success: (res) => {
					res.eventChannel.emit('acceptDataFormOpener', {
						transactionForm,
						isUpdate,
						stockId: this.stockId
					})

					// 监听明细保存事件
					res.eventChannel.on('saveTransactionDetail', (data) => {
						this.handleSaveTransactionDetail(data)
					})
				}
			})
		},

		// 处理交易明细保存
		handleSaveTransactionDetail(data) {
			try {
				const { transactionDetail, isUpdate } = data

				if (isUpdate) {
					// 更新现有明细
					const index = this.transactionList.findIndex(item => {
						// 如果有ID，按ID匹配；否则按tempId匹配
						if (transactionDetail.id && item.id) {
							return item.id === transactionDetail.id
						}
						if (transactionDetail.tempId && item.tempId) {
							return item.tempId === transactionDetail.tempId
						}
						return false
					})

					if (index !== -1) {
						// 更新现有项
						this.transactionList.splice(index, 1, { ...transactionDetail })
					} else {
						// 如果找不到匹配项，作为新增处理
						this.transactionList.push({ ...transactionDetail })
					}
				} else {
					// 新增明细
					// 为新增的明细生成临时ID
					const tempId = Date.now() + Math.random()
					const newDetail = {
						...transactionDetail,
						tempId: tempId,
						inventoryId: this.stockId
					}
					this.transactionList.push(newDetail)
				}

				// 触发页面更新
				this.$forceUpdate()

			} catch (error) {
				console.error('处理交易明细保存失败:', error)
				uni.showToast({
					title: '保存明细失败',
					icon: 'none'
				})
			}
		},

		// 删除交易明细
		deleteTransaction(transaction) {
			uni.showModal({
				title: '提示',
				content: `确定要删除这条交易明细吗？`,
				cancelText: '取消',
				confirmText: '确定',
				success: (res) => {
					if (res.confirm) {
						try {
							// 从数组中删除该明细
							this.transactionList = this.transactionList.filter(item => {
								// 如果有ID，按ID比较；否则按tempId比较
								if (transaction.id && item.id) {
									return item.id !== transaction.id
								} else if (transaction.tempId && item.tempId) {
									return item.tempId !== transaction.tempId
								} else {
									// 如果都没有ID，按对象引用比较
									return item !== transaction
								}
							})

							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						} catch (error) {
							console.error('删除交易明细失败:', error)
							uni.showToast({
								title: '删除失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},

		// 获取移动类型文本
		getMoveTypeText(moveType) {
			if (!this.dictData || !this.dictData[DICT_TYPE.INVENTORY_MOVE_TYPE]) {
				return moveType || '-'
			}
			const option = this.dictData[DICT_TYPE.INVENTORY_MOVE_TYPE].find(item => item.value === moveType)
			return option ? option.label : (moveType || '-')
		},

		// 保存数据
		async handleSave() {
			if (!this.formData) return

			try {
				// 表单验证
				await this.$refs.formRef.validate()

				this.saving = true

				// 准备保存数据，包含交易明细
				const saveData = {
					...this.formData,
					transactionList: this.transactionList || []
				}

				await stockInfoApi.updateStockInfo(JSON.stringify(saveData))

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)

			} catch (error) {
				console.error('保存失败:', error)
				if (error.errorFields) {
					// 表单验证失败
					uni.showToast({
						title: '请检查表单输入',
						icon: 'error'
					})
				} else {
					// 接口调用失败
					uni.showToast({
						title: '保存失败',
						icon: 'error'
					})
				}
			} finally {
				this.saving = false
			}
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0'
			if (isNaN(value)) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '-'
			const date = new Date(timestamp)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId && unitId !== 0) return ''

			// 确保unitOptions存在且为数组
			if (!Array.isArray(this.unitOptions)) {
				return unitId.toString()
			}

			// 处理数字类型
			if (typeof unitId === 'number') {
				const unit = this.unitOptions.find(unit => unit.id === unitId)
				if (unit && unit.name) return unit.name
			}

			// 处理字符串类型
			if (typeof unitId === 'string') {
				// 先尝试按字符串查找
				let unit = this.unitOptions.find(unit => unit.id && unit.id.toString() === unitId)
				if (unit && unit.name) return unit.name

				// 再尝试转换为数字查找
				const numId = parseInt(unitId)
				if (!isNaN(numId)) {
					unit = this.unitOptions.find(unit => unit.id === numId)
					if (unit && unit.name) return unit.name
				}
			}

			return unitId ? unitId.toString() : ''
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			if (!type) return '-'
			const option = this.materialTypeOptions.find(item => item.value === type)
			return option ? option.label : type
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			if (!source) return '-'
			const option = this.materialSourceOptions.find(item => item.value === source)
			return option ? option.label : source
		},

		// 获取状态文本
		getStatusText(status) {
			if (!status) return '-'
			const option = this.statusOptions.find(item => item.value === status)
			return option ? option.label : status
		},

		// 获取交易类型文本
		getTransactionTypeText(type) {
			if (!type) return '-'
			const options = this.dictData[DICT_TYPE.SCM_BIZ_TYPE] || []
			const option = options.find(item => item.value === type)
			return option ? option.label : type
		},

		// 获取交易方向文本
		getTransactionDirectionText(direction) {
			if (!direction) return '-'
			const options = this.dictData[DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION] || []
			const option = options.find(item => item.value === direction)
			return option ? option.label : direction
		},

	}
}
</script>

<style lang="scss" scoped>
.edit-container {
	height: 100vh;
	width: 100vw;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
}

.form-content {
	flex: 1;
	padding: 32rpx 40rpx;
	padding-bottom: 140rpx; // 为底部按钮留出空间
}

/* 添加明细按钮 */
.add-transaction-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background-color: #007aff;
	color: white;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	font-weight: 500;
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.add-icon {
	font-size: 32rpx;
	font-weight: bold;
}

.add-text {
	font-size: 28rpx;
}

/* 空状态 */
.empty-transactions {
	text-align: center;
	padding: 80rpx 40rpx;
	color: #999;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 交易明细列表 */
.transaction-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.transaction-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	border: 1px solid #f0f0f0;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 交易头部信息 */
.transaction-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 1px solid #f0f0f0;
}

.transaction-info-header {
	flex: 1;
}

.transaction-type {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 8rpx;
}

.transaction-no {
	font-size: 26rpx;
	color: #7f8c8d;
}

/* 操作按钮 */
.transaction-actions {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.edit-btn {
	background-color: #f8f9fa;
	border: 1px solid #dee2e6;
}

.delete-btn {
	background-color: #f8f9fa;
	border: 1px solid #dee2e6;
}

.action-icon {
	font-size: 28rpx;
}

/* 交易详细信息 */
.transaction-details {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 详情分组 */
.detail-section {
	background-color: #fafafa;
	border-radius: 8rpx;
	padding: 16rpx;
	border: 1px solid #f0f0f0;
}

.section-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #495057;
	margin-bottom: 12rpx;
	display: block;
	padding-bottom: 8rpx;
	border-bottom: 1px solid #e8e8e8;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 12rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.detail-item.full-width {
	flex: none;
	width: 100%;
}

.detail-label {
	font-size: 24rpx;
	color: #7f8c8d;
	line-height: 1.2;
}

.detail-value {
	font-size: 28rpx;
	color: #2c3e50;
	line-height: 1.3;
	font-weight: 400;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 重要信息突出显示 */
.detail-value.important {
	font-weight: bold;
	color: #2c3e50;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 32rpx 40rpx;
	border-top: 1px solid #eee;
	z-index: 100;

	.save-button {
		width: 100%;
		height: 88rpx;
		background-color: #007aff;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-button:disabled {
		background-color: #c0c4cc;
		color: #ffffff;
		cursor: not-allowed;
	}
}

.empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 60vh;

	.empty-content {
		text-align: center;

		.empty-text {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 40rpx;
			display: block;
		}
	}
}

.transaction-list {
	.transaction-item {
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		border: 1px solid #e9ecef;

		&:last-child {
			margin-bottom: 0;
		}

		.transaction-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			padding-bottom: 16rpx;
			border-bottom: 1px solid #dee2e6;

			.transaction-type {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
			}

			.transaction-date {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-details {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.transaction-no {
				font-size: 26rpx;
				color: #495057;
			}

			.transaction-batch {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-quantities {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.transaction-quantity {
				font-size: 26rpx;
				color: #495057;
			}

			.lock-quantity {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-before-after {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.before-quantity {
				font-size: 26rpx;
				color: #495057;
			}

			.after-quantity {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-warehouse {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.from-warehouse {
				font-size: 26rpx;
				color: #495057;
			}

			.to-warehouse {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-remark {
			margin-top: 16rpx;
			padding-top: 16rpx;
			border-top: 1px solid #dee2e6;

			.remark-text {
				font-size: 26rpx;
				color: #495057;
				line-height: 1.5;
			}
		}
	}
}

.no-data {
	text-align: center;
	padding: 80rpx 0;
	color: #6c757d;
	font-size: 28rpx;
}

.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
